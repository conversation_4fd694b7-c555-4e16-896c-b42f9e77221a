<div class="modal fade" id="add_learning_devlopment_modal" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="false">
    <div class="modal-dialog" role="document" style="max-width:1000px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Create IIY</h5>
                <button type="button" class="btn btn-label-danger btn-icon" data-bs-dismiss="modal"><i
                        class="fa fa-times"></i></button>
            </div>

            <div class="modal-body">
                <div class="card-body">

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Topics Learnt <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="topics_learnt" name="topics_learnt">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Topics Present <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="topics_present" name="topics_present">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Hours Invested in IIY <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" max="999" id="hours_invested"
                                    name="hours_invested" maxlength="3"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>
                    </div>



                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Vendor Facility Visited <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" id="vendor_facility_visited"
                                    name="vendor_facility_visited"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>



                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Conferences Attended <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" id="conferences_attended"
                                    name="conferences_attended"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Exhibitions Attended <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" id="exhibitions_attended"
                                    name="exhibitions_attended"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Consumer & Retail Visits <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" id="consumer_retail_visits"
                                    name="consumer_retail_visits"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No of Ideas Generated Through IIY <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control" min="-1" id="ideas_generated"
                                    name="ideas_generated"
                                    oninput="if(this.value.length > 3) this.value = this.value.slice(0, 3);">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Select Date <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                    <input type="text" class="form-control" id="date_range" name="date_range"
                                        placeholder="Select Date">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 pt-4">
                            <div class="form-group">
                                <label class="form-label">Upload the Presentation <span
                                        class="text-danger"></span></label>
                                <input type="file" class="form-control" id="weekly_presentation"
                                    name="weekly_presentation[]" accept="application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document,
                                            application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation, 
                                            .doc, .docx, .ppt, .pptx, .pdf" multiple>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="create_iiy_modal_submit_btn" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/components/modal/iiy-create-modal.blade.php ENDPATH**/ ?>