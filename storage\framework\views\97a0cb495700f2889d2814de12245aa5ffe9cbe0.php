<?php echo $__env->make('layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body class="preload-active">
<?php echo $__env->make('sweetalert::alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <div class="preload">
        <div class="preload-dialog"><div class="spinner-border text-primary preload-spinner"></div></div>
    </div>
    <style>
.button {
  background-color: #4CAF50; /* Green */
  border: none;
  color: white;
  padding: 16px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 30px 30px;
  transition-duration: 0.4s;
  cursor: pointer;
}

 
.button2 {
  background-color: white; 
  color: black; 
  border: 2px solid #008CBA;
}

.button2:hover {
  background-color: #008CBA;
  color: white;
}

 
</style>
    <div class="holder">
        <div class="wrapper">
            <div class="content">
                <div class="container-fluid">
                    <div class="row g-0 align-items-center justify-content-center h-100">
                        <div class="col-sm-8 col-md-6 col-lg-4 col-xl-3">
                            <div class="portlet">
                                <div class="portlet-body">
                                    <div class="text-center mt-4 mb-5">
                                        <div class="avatar avatar-label-primary avatar-circle widget12">

                                            <div class="avatar-display" style="padding-top: 10px;"><i class="fa fa-user-alt"></i></div>
                                        </div>
                                    </div>
                                    <!-- <form method="POST" action="<?php echo e(route('login')); ?>">
                                        <?php echo csrf_field(); ?>

 
                                        <div class="validation-container">
                                            <div class="form-floating">
                                                <input class="form-control form-control-lg <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="email" id="email" name="email" placeholder="Please insert your email" value="<?php echo e(old('email')); ?>" required autocomplete="email"  />
                                                <label for="email"><?php echo e(__('Email Address')); ?></label>
                                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback" role="alert">
                                                    <strong><?php echo e($message); ?></strong>
                                                </span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <br>
                                        </div>

                                        <div class="validation-container">
                                            <div class="form-floating">
                                                <input class="form-control form-control-lg <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="password" id="password" name="password" placeholder="Please insert your password" autocomplete="current-password" />
                                                <label for="password"><?php echo e(__('Password')); ?></label>
                                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="invalid-feedback" role="alert">
                                                        <strong><?php echo e($message); ?></strong>
                                                    </span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <br>
                                        </div>

                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="row mb-4">
                                                <div class="col">

                                                </div>

                                            </div>

                                        </div> 



                                        <div class="d-flex align-items-center justify-content-between">
                                            
                                     <button type="submit" class="btn btn-label-primary btn-lg btn-widest"><?php echo e(__('Login')); ?></button>
                                        </div>
 
                                    </form> -->

                                      <!-- <a href="login/microsoft_hemas" class="btn-block btn btn-fill btn-info text-center btn-align" style="height:40px;width:300px;font-size:15px;"><i class="fa-brands fa-microsoft"></i> Sign in with  Microsoft Hemas
                              </a>    
                            <a href="login/microsoft_cavincare" class="btn-block btn btn-fill btn-info text-center btn-align" style="height:40px;width:300px;font-size:15px;margin-top:10px;"> <i class="fa-brands fa-microsoft"></i> Sign in with  Microsoft Cavincare
                              </a>   -->
                              <a href="login/microsoft_cavincare"   style="height:40px;width:400px;font-size:15px;margin-top:10px;"><button class="button button2">  <i class="fa-brands fa-microsoft"></i>  Sign in with  Microsoft Cavinkare</button> 
                                            </a>  
                             
                              
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="floating-btn floating-btn-end d-grid gap-2">
        <button class="btn btn-flat-primary btn-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Change theme" id="theme-toggle"><i class="fa fa-moon"></i></button>
    </div>
    <script type="text/javascript" src="<?php echo e(asset('build/scripts/mandatory.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('build/scripts/core.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('build/scripts/vendor.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('app/utilities/copyright-year.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('app/utilities/theme-switcher.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('app/utilities/tooltip-popover.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('app/utilities/dropdown-scrollbar.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('app/pages/pages/login.js')); ?>"></script>
</body>
</html>













<?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/auth/login.blade.php ENDPATH**/ ?>