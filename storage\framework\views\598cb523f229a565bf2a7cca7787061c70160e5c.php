<div class="modal fade" id="edit_insights_modal" tabindex="-1" role="dialog" aria-labelledby="editInsightsLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editInsightsLabel">Edit Insights</h5>
                <button type="button" class="btn btn-label-danger btn-icon" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <form id="edit_learning_development_insights">
                <div class="modal-body">
                    <div class="card-body">
                        <input type="hidden" name="edit_insight_id" id="edit_insight_id">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">TOPICS GENERATED</label>
                                    <input type="text" class="form-control" name="edit_insight_topic" id="edit_insight_topic" >
                                </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                            <div class="mb-3">
                                <label class="form-label">Insights Category <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="edit_insight_category" id="edit_insight_category" style="width:100%;">
                                    <option value="">Select</option>
                                    <?php $__currentLoopData = $insightcategoryhr; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->source); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                            <div class="mb-3">
                                <label class="form-label">Packaging Technologist <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="edit_scientist_insights" id="edit_scientist_insights" style="width:100%;" disabled>
                                        <?php $__currentLoopData = $scientist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($scient->id); ?>"><?php echo e($scient->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                            </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                            <div class="mb-3">
                                <label class="form-label">Remarks <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="edit_insight_remarks" id="edit_insight_remarks" placeholder="Enter Remarks"></textarea>
                            </div>
                            </div>
                            </div>
                            </div>
                            </div>

                           
                    
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/components/modal/insights-edit-modal.blade.php ENDPATH**/ ?>