<div class="modal fade" id="edit_preferedconcepts_modal" tabindex="-1" role="dialog"
    aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Edit Idea</h5>
                <button type="button" id="btn_close_edit_ideas" class="btn btn-label-danger btn-icon btn_close_ideas" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <form id="edit_learning_development_idea" method="post" action="javascript:void(0)">
                <input type="hidden" name="edit_idea_id" id="edit_idea_id">
                <div class="modal-body" id="edit_refresh_ideas">
                    <div class="card-body">
                        <div class="row">
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Category</label>
                                    <select class="form-control select2" name="edit_idea_category" id="edit_idea_category" style="width:100%;">
                                        <option value="0">Select</option>
                                        <option value="1">Parent Idea</option>
                                    </select>
                                </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Type</label>
                                    <select class="form-control select2" name="edit_idea_type" id="edit_idea_type" style="width:100%;">
                                        <option value="0">Select</option>
                                        <?php $__currentLoopData = $ideatype; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type->id); ?>"><?php echo e($type->parent_idea); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Insights</label>
                                    <select class="form-control select2" name="edit_idea_insight[]" multiple id="edit_idea_insight" style="width:100%;">
                                        <option value="0">Select</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Description</label>
                                    <textarea class="form-control" name="edit_idea_txt" id="edit_idea_txt" placeholder="Enter description of the idea"></textarea>
                                </div>
                            </div>
                           
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">CO Creator</label>
                                    <select class="form-control select2" name="edit_idea_scientist[]" id="edit_idea_scientist" style="width:100%;" multiple>
                                    </select>
                                </div>
                            </div>
                           
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Comments</label>
                                    <textarea class="form-control" name="edit_ideas_comments" id="edit_ideas_comments" placeholder="Enter your Equipped skill level"></textarea>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Target Date</label>
                                    <input type="date" class="form-control" name="edit_target_date" id="edit_target_date">
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn_close_ideas" data-bs-dismiss="modal">Close</button>
                    <button type="submit" onclick="update_idea()" id="update_learning_idea" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/components/modal/idea-edit-modal.blade.php ENDPATH**/ ?>