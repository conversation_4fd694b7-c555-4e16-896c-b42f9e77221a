<script type="text/javascript" src="<?php echo e(URL::asset('build/scripts/mandatory.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('build/scripts/core.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('build/scripts/vendor.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/utilities/copyright-year.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/utilities/theme-switcher.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/utilities/tooltip-popover.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/utilities/dropdown-scrollbar.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/pages/form/select2.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/pages/elements/sweet-alert.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(URL::asset('app/pages/datatable/advanced/column-rendering.js')); ?>"></script>


<script src="<?php echo e(URL::asset('external_assets/jquery-confirm.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('external_assets/jquery.uploadfile.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('external_assets/alert/alertify.js')); ?>"></script>
<script src="<?php echo e(URL::asset('external_assets/alert/alertify.min.js')); ?>"></script>
<script src="<?php echo e(asset('app/pages/jquery-searchbox.js')); ?>"></script>

 <script src="<?php echo e(URL::asset('external_assets/modal-loading.js')); ?>"></script>

<script>
  $('.js-searchBox').searchBox({ elementWidth: '250'});
    if ($('#authid').val() == 74) {
        $('button[type="button"]').hide();
        $('button[type="submit"]').hide();
    }
    
$(document).ready(function(){
    var originalSize = $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size');
  // reset
   $(".resetMe").click(function(){
  $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size', originalSize); 

   });

   // Increase Font Size
   $(".increase").click(function(){
  var currentSize = $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size');
  var currentSize = parseFloat(currentSize)*1.2;
  $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size', currentSize);

  return false;
   });

   // Decrease Font Size
   $(".decrease").click(function(){
  //var currentFontSize = $('div').css('font-size');
    var currentSize = $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size');
    var currentSize = parseFloat(currentSize)*0.8;
    $('div,p,h1,h2,h3,h4,h5,h6,span,a,button,i,table thead th, tbody td').css('font-size', currentSize);

    return false;
   });


   $("#font_size").click(function(e){
  if ($('.font_btn').is(':hidden'))
  {
    $(".font_btn").show();
    $(".font_color_btn").hide();
    $("#chat-section-window").hide();
    $("#font_size").addClass("bg_fs_color");
    $("#font_color").removeClass("bg_fs_color");
    $("#chat-section").removeClass("bg_fs_color");
     e.stopPropagation();
  }else
  {
    $(".font_btn").hide();
  }
});

$(document).click(function(){
    $(".font_btn").hide();
    $("#font_size").removeClass("bg_fs_color");
});

});



 // Get the color picker input element and reset button element
 const colorPicker = document.getElementById('colr_pick');
 const resetButton = document.getElementById('reset-button');

// Function to change the font color of all elements

function changeFontColor(color) {
  // Get all elements in the document
  const allElements = document.getElementsByTagName('*');


  // Loop through all elements and apply the new font color with !important
  for (let i = 0; i < allElements.length; i++) {
    allElements[i].style.setProperty('color', color, 'important');
  }
}

// Function to reset font colors to default
function resetFontColors() {
  // Get all elements in the document
  const allElements = document.getElementsByTagName('*');

  // Loop through all elements and remove the color property
  for (let i = 0; i < allElements.length; i++) {
    allElements[i].style.removeProperty('color');
  }
}

// Event listener for when a color is picked
// colorPicker.addEventListener('input', function() {
//   const selectedColor = colorPicker.value;
//   changeFontColor(selectedColor);
// });

// Event listener for the reset button
// resetButton.addEventListener('click', function() {
//   resetFontColors();
// });


$("#font_color").click(function(e){
  if ($('.font_color_btn').is(':hidden'))
  {
    $(".font_color_btn").show();
    // $("#font_color").css("background-color" , "yellow");
    $("#font_color").addClass("bg_fs_color");
    $("#font_size").removeClass("bg_fs_color");
    $("#chat-section").removeClass("bg_fs_color");
    $(".font_btn").hide();
    $("#chat-section-window").hide();
     e.stopPropagation();
  }else
  {
    // $(".font_color_btn").hide();

  }
});

$(document).click(function(){
    $(".font_color_btn").hide();
    $("#font_color").removeClass("bg_fs_color");
});



</script>
<!-- <script type="text/javascript" src="<?php echo e(asset('app/pages/datatable/advanced/dataTables.rowsGroup.js')); ?>"></script> --><?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/layouts/vendor-scripts.blade.php ENDPATH**/ ?>